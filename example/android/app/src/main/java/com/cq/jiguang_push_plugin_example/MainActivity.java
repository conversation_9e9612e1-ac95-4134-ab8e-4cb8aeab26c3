package com.cq.jiguang_push_plugin_example;

import android.content.Intent;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.embedding.android.FlutterActivity;

public class MainActivity extends FlutterActivity {
    private static final String TAG = "MainActivity";

    @Override
    protected void onNewIntent(@NonNull Intent intent) {
        super.onNewIntent(intent);
        if (getIntent() != null) {
            String value1 = getIntent().getStringExtra("key1");
            int value2 = getIntent().getIntExtra("key2", 0);
            Log.e(TAG, "onNewIntent: value1: " + value1 + ", value2: " + value2);
        }
    }
}
