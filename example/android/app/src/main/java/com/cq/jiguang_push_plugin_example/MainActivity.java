package com.cq.jiguang_push_plugin_example;

import android.app.AlertDialog;
import android.content.Intent;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.embedding.android.FlutterActivity;

public class MainActivity extends FlutterActivity {
    private static final String TAG = "MainActivity";

    @Override
    protected void onNewIntent(@NonNull Intent intent) {
        super.onNewIntent(intent);
        if (getIntent() != null) {
            String value1 = getIntent().getStringExtra("key1");
            int value2 = getIntent().getIntExtra("key2", 0);
            Log.e(TAG, "onNewIntent: value1: " + value1 + ", value2: " + value2);

            // 显示接收到的值的 Dialog
            showReceivedValuesDialog(value1, value2);
        }
    }

    private void showReceivedValuesDialog(String value1, int value2) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("接收到的值");

        StringBuilder message = new StringBuilder();
        message.append("key1: ").append(value1 != null ? value1 : "null").append("\n");
        message.append("key2: ").append(value2);

        builder.setMessage(message.toString());
        builder.setPositiveButton("确定", (dialog, which) -> dialog.dismiss());

        AlertDialog dialog = builder.create();
        dialog.show();
    }
}
