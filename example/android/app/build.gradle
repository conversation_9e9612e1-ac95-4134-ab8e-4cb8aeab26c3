plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.cq.jiguang_push_plugin_example"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.changqing.health"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        manifestPlaceholders = [
                JPUSH_PKGNAME : applicationId,
                JPUSH_APPKEY  : "fb14eb189466ab13e928e7ae", //JPush 上注册的包名对应的 Appkey.
                JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.

                //meizu_config_start
                MEIZU_APPKEY  : "MZ-魅族的APPKEY",
                MEIZU_APPID   : "MZ-魅族的APPID",
                //meizu_config_end
                //xiaomi_config_start
                XIAOMI_APPID  : "MI-小米的APPID",
                XIAOMI_APPKEY : "MI-小米的APPKEY",
                //xiaomi_config_end
                //oppo_config_start
                OPPO_APPKEY   : "OP-8b0f5a8604394f508271a13dd1325a24",
                OPPO_APPID    : "***********",
                OPPO_APPSECRET: "OP-25dc20b7da0543c8a3212df308b9bbeb",
                //oppo_config_end
                //vivo_config_start
                VIVO_APPKEY   : "e3ba1a2c19634cbb8bd8fbe7c7e79761",
                VIVO_APPID    : "105916775",
                //vivo_config_end
                //honor_config_start
                HONOR_APPID   : "honor的APPID",
                //honor_config_end
                //nio_config_start
                NIO_APPID     : "蔚来的APPID",
                //nio_config_end

        ]
    }

    signingConfigs {
        debug {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }

        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
//            signingConfig = signingConfigs.debug
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig = signingConfigs.release
            ndk { abiFilters 'arm64-v8a' }
        }
    }
}

flutter {
    source = "../.."
}